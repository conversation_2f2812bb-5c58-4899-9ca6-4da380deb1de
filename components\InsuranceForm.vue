<template>
  <div class="bg-white border border-[#E9ECEE] rounded-lg p-8 max-w-[1140px] mx-auto">
    <!-- Form Title -->
    <div class="mb-8">
      <h1 class="text-2xl font-semibold text-[#1A1A1A]">{{ formTitle }}</h1>
    </div>

    <!-- Form Content -->
    <form @submit.prevent="handleSubmit" class="space-y-6">
      <!-- Owner Information Section -->
      <div v-if="showOwnerInfo" class="space-y-6">
        <h2 class="text-lg font-medium text-[#333333] border-b border-[#E9ECEE] pb-2">
          Thông tin chủ xe
        </h2>
        
        <!-- Company Name -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Tên công ty <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.companyName"
            type="text"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập tên công ty"
            maxlength="200"
          />
          <span v-if="errors.companyName" class="text-red-500 text-xs">{{ errors.companyName }}</span>
        </div>

        <!-- Tax Code -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Mã số thuế <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.taxCode"
            type="text"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập mã số thuế"
            maxlength="200"
          />
          <span v-if="errors.taxCode" class="text-red-500 text-xs">{{ errors.taxCode }}</span>
        </div>

        <!-- Company Address -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Địa chỉ công ty <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.companyAddress"
            type="text"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập địa chỉ công ty"
          />
          <span v-if="errors.companyAddress" class="text-red-500 text-xs">{{ errors.companyAddress }}</span>
        </div>

        <!-- Phone Number -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Số điện thoại <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.phoneNumber"
            type="tel"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập số điện thoại"
            maxlength="10"
            pattern="[0-9]*"
          />
          <span v-if="errors.phoneNumber" class="text-red-500 text-xs">{{ errors.phoneNumber }}</span>
        </div>

        <!-- Email -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Email nhận GCN bảo hiểm <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.email"
            type="email"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập email"
          />
          <span v-if="errors.email" class="text-red-500 text-xs">{{ errors.email }}</span>
        </div>

        <!-- Save Info Checkbox -->
        <div v-if="!readonly" class="flex items-center gap-2">
          <input
            v-model="formData.saveOwnerInfo"
            type="checkbox"
            id="saveOwnerInfo"
            class="w-4 h-4 text-[#0D68B2] border-gray-300 rounded focus:ring-[#0D68B2]"
          />
          <label for="saveOwnerInfo" class="text-sm text-[#333333]">
            Lưu thông tin chủ xe
          </label>
        </div>
      </div>

      <!-- Vehicle Information Section -->
      <div v-if="showVehicleInfo" class="space-y-6">
        <h2 class="text-lg font-medium text-[#333333] border-b border-[#E9ECEE] pb-2">
          Thông tin xe
        </h2>
        
        <!-- License Plate -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Biển kiểm soát <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.licensePlate"
            type="text"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập biển kiểm soát"
            maxlength="200"
          />
          <span v-if="errors.licensePlate" class="text-red-500 text-xs">{{ errors.licensePlate }}</span>
        </div>

        <!-- Number of Seats -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Số chỗ <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.numberOfSeats"
            type="number"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập số chỗ"
            min="1"
            max="9"
          />
          <span v-if="errors.numberOfSeats" class="text-red-500 text-xs">{{ errors.numberOfSeats }}</span>
        </div>

        <!-- Chassis Number -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">Số khung</label>
          <input
            v-model="formData.chassisNumber"
            type="text"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập số khung"
            maxlength="200"
          />
        </div>

        <!-- Engine Number -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">Số máy</label>
          <input
            v-model="formData.engineNumber"
            type="text"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
            placeholder="Nhập số máy"
            maxlength="200"
          />
        </div>

        <!-- Weight Capacity (Disabled) -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">Trọng tải</label>
          <input
            type="text"
            value="Trên 15 tấn"
            disabled
            class="w-full px-3 py-2 border rounded-md text-sm bg-gray-50 border-gray-200 text-gray-700"
          />
        </div>

        <!-- Vehicle Type (Disabled) -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">Loại xe</label>
          <input
            type="text"
            value="Xe ô tô chở hàng (Xe tải)"
            disabled
            class="w-full px-3 py-2 border rounded-md text-sm bg-gray-50 border-gray-200 text-gray-700"
          />
        </div>

        <!-- Purpose (Disabled) -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">Mục đích sử dụng</label>
          <input
            type="text"
            value="Kinh doanh vận tải"
            disabled
            class="w-full px-3 py-2 border rounded-md text-sm bg-gray-50 border-gray-200 text-gray-700"
          />
        </div>
      </div>

      <!-- Insurance Period Section -->
      <div v-if="showInsurancePeriod" class="space-y-6">
        <h2 class="text-lg font-medium text-[#333333] border-b border-[#E9ECEE] pb-2">
          Thời hạn bảo hiểm
        </h2>
        
        <!-- Start Date -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">
            Ngày bắt đầu <span class="text-red-500">*</span>
          </label>
          <input
            v-model="formData.startDate"
            type="datetime-local"
            :disabled="readonly"
            :class="[
              'w-full px-3 py-2 border rounded-md text-sm',
              readonly 
                ? 'bg-gray-50 border-gray-200 text-gray-700' 
                : 'border-[#E9ECEE] focus:border-[#0D68B2] focus:outline-none'
            ]"
          />
          <span v-if="errors.startDate" class="text-red-500 text-xs">{{ errors.startDate }}</span>
        </div>

        <!-- End Date (Disabled) -->
        <div class="flex flex-col gap-2">
          <label class="text-sm font-medium text-[#333333]">Ngày kết thúc</label>
          <input
            :value="endDate"
            type="datetime-local"
            disabled
            class="w-full px-3 py-2 border rounded-md text-sm bg-gray-50 border-gray-200 text-gray-700"
          />
        </div>
      </div>

      <!-- Fee Information Section (for confirmation step) -->
      <div v-if="showFeeInfo" class="space-y-6">
        <h2 class="text-lg font-medium text-[#333333] border-b border-[#E9ECEE] pb-2">
          Phí bảo hiểm
        </h2>
        
        <div class="bg-gray-50 p-4 rounded-lg space-y-3">
          <div class="flex justify-between">
            <span class="text-sm text-[#333333]">Phí chưa VAT:</span>
            <span class="text-sm font-medium text-[#333333]">266.666đ</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-[#333333]">Thuế VAT:</span>
            <span class="text-sm font-medium text-[#333333]">26.667đ</span>
          </div>
          <div class="flex justify-between">
            <span class="text-sm text-[#333333]">Tổng phí (gồm VAT):</span>
            <span class="text-sm font-medium text-[#333333]">293.333đ</span>
          </div>
          <div class="border-t pt-3 flex justify-between">
            <span class="text-base font-semibold text-[#0D68B2]">TỔNG PHÍ THANH TOÁN:</span>
            <span class="text-base font-semibold text-[#0D68B2]">293.333đ</span>
          </div>
        </div>
      </div>

      <!-- Agreement Checkbox (for confirmation step) -->
      <div v-if="showAgreement" class="space-y-4">
        <div class="flex items-start gap-3">
          <input
            v-model="formData.agreement"
            type="checkbox"
            id="agreement"
            class="w-4 h-4 text-[#0D68B2] border-gray-300 rounded focus:ring-[#0D68B2] mt-1"
          />
          <label for="agreement" class="text-sm text-[#333333] leading-relaxed">
            Tôi đồng ý đã đọc, hiểu các quy định Pháp luật về 
            <a href="#" target="_blank" class="text-[#0D68B2] underline hover:no-underline">
              Bảo hiểm bắt buộc trách nhiệm dân sự của chủ xe ô tô
            </a>
          </label>
        </div>
      </div>

      <!-- Action Buttons -->
      <div v-if="!readonly" class="flex justify-end gap-4 pt-6">
        <button
          v-if="showBackButton"
          type="button"
          @click="$emit('back')"
          class="px-6 py-2 border border-[#E9ECEE] text-[#333333] rounded-md hover:bg-gray-50 transition-colors"
        >
          Quay lại
        </button>
        <button
          type="submit"
          :disabled="submitDisabled"
          :class="[
            'px-6 py-2 rounded-md font-medium transition-colors',
            submitDisabled
              ? 'bg-gray-300 text-gray-500 cursor-not-allowed'
              : 'bg-[#0D68B2] text-white hover:bg-[#0B5A9A]'
          ]"
        >
          {{ submitButtonText }}
        </button>
      </div>
    </form>
  </div>
</template>

<script setup lang="ts">
interface FormData {
  // Owner info
  companyName: string
  taxCode: string
  companyAddress: string
  phoneNumber: string
  email: string
  saveOwnerInfo: boolean
  
  // Vehicle info
  licensePlate: string
  numberOfSeats: string
  chassisNumber: string
  engineNumber: string
  
  // Insurance period
  startDate: string
  
  // Agreement
  agreement: boolean
}

interface Props {
  formTitle: string
  readonly?: boolean
  showOwnerInfo?: boolean
  showVehicleInfo?: boolean
  showInsurancePeriod?: boolean
  showFeeInfo?: boolean
  showAgreement?: boolean
  showBackButton?: boolean
  submitButtonText?: string
  initialData?: Partial<FormData>
}

const props = withDefaults(defineProps<Props>(), {
  readonly: false,
  showOwnerInfo: true,
  showVehicleInfo: true,
  showInsurancePeriod: true,
  showFeeInfo: false,
  showAgreement: false,
  showBackButton: false,
  submitButtonText: 'Tiếp tục'
})

const emit = defineEmits<{
  submit: [data: FormData]
  back: []
}>()

// Form data
const formData = ref<FormData>({
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  saveOwnerInfo: false,
  licensePlate: '',
  numberOfSeats: '',
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  agreement: false,
  ...props.initialData
})

// Errors
const errors = ref<Partial<Record<keyof FormData, string>>>({})

// Computed
const endDate = computed(() => {
  if (!formData.value.startDate) return ''
  const start = new Date(formData.value.startDate)
  const end = new Date(start.getTime() + 30 * 24 * 60 * 60 * 1000) // Add 30 days
  return end.toISOString().slice(0, 16) // Format for datetime-local
})

const submitDisabled = computed(() => {
  if (props.showAgreement) {
    return !formData.value.agreement
  }
  return false
})

// Methods
const validateForm = (): boolean => {
  errors.value = {}
  let isValid = true

  if (props.showOwnerInfo) {
    if (!formData.value.companyName.trim()) {
      errors.value.companyName = 'Tên công ty là bắt buộc'
      isValid = false
    }
    if (!formData.value.taxCode.trim()) {
      errors.value.taxCode = 'Mã số thuế là bắt buộc'
      isValid = false
    }
    if (!formData.value.companyAddress.trim()) {
      errors.value.companyAddress = 'Địa chỉ công ty là bắt buộc'
      isValid = false
    }
    if (!formData.value.phoneNumber.trim()) {
      errors.value.phoneNumber = 'Số điện thoại là bắt buộc'
      isValid = false
    } else if (!/^\d{10}$/.test(formData.value.phoneNumber)) {
      errors.value.phoneNumber = 'Số điện thoại phải có 10 chữ số'
      isValid = false
    }
    if (!formData.value.email.trim()) {
      errors.value.email = 'Email là bắt buộc'
      isValid = false
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.value.email)) {
      errors.value.email = 'Email không hợp lệ'
      isValid = false
    }
  }

  if (props.showVehicleInfo) {
    if (!formData.value.licensePlate.trim()) {
      errors.value.licensePlate = 'Biển kiểm soát là bắt buộc'
      isValid = false
    }
    if (!formData.value.numberOfSeats.trim()) {
      errors.value.numberOfSeats = 'Số chỗ là bắt buộc'
      isValid = false
    } else if (!/^[1-9]$/.test(formData.value.numberOfSeats)) {
      errors.value.numberOfSeats = 'Số chỗ phải là số từ 1-9'
      isValid = false
    }
  }

  if (props.showInsurancePeriod) {
    if (!formData.value.startDate) {
      errors.value.startDate = 'Ngày bắt đầu là bắt buộc'
      isValid = false
    }
  }

  return isValid
}

const handleSubmit = () => {
  if (validateForm()) {
    emit('submit', formData.value)
  }
}

// Initialize form data with current datetime
onMounted(() => {
  if (!formData.value.startDate) {
    const now = new Date()
    formData.value.startDate = now.toISOString().slice(0, 16)
  }
})
</script>

<style scoped>
/* Custom input focus styles */
input:focus {
  box-shadow: 0 0 0 2px rgba(13, 104, 178, 0.1);
}

/* Custom checkbox styles */
input[type="checkbox"]:checked {
  background-color: #0D68B2;
  border-color: #0D68B2;
}
</style>
