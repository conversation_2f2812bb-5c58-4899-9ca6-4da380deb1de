<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <Header />

    <!-- Main Content -->
    <main class="max-w-[1920px] mx-auto px-4 lg:px-[390px] py-8">
      <!-- Steps Indicator -->
      <div class="mb-8">
        <StepsIndicator :current-step="1" />
      </div>

      <!-- Insurance Form -->
      <InsuranceForm
        form-title="Khai báo thông tin mua bảo hiểm"
        :show-owner-info="true"
        :show-vehicle-info="true"
        :show-insurance-period="true"
        :show-fee-info="false"
        :show-agreement="false"
        :show-back-button="false"
        submit-button-text="Tiếp tục"
        :initial-data="formData"
        @submit="handleSubmit"
      />
    </main>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup lang="ts">
import Header from '~/components/Header.vue'
import Footer from '~/components/Footer.vue'
import StepsIndicator from '~/components/StepsIndicator.vue'
import InsuranceForm from '~/components/InsuranceForm.vue'

// Set page title
useHead({
  title: '<PERSON><PERSON> bảo hiểm - Bước 1: Khai báo thông tin'
})

// Form data
const formData = ref({
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  saveOwnerInfo: false,
  licensePlate: '',
  numberOfSeats: '',
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  agreement: false
})

// Handle form submission
const handleSubmit = (data: any) => {
  // Save data to session storage or store
  if (process.client) {
    sessionStorage.setItem('insuranceFormData', JSON.stringify(data))
  }
  
  // Navigate to step 2
  navigateTo('/insurance/buy/step2')
}

// Load saved data on mount
onMounted(() => {
  if (process.client) {
    const savedData = sessionStorage.getItem('insuranceFormData')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        formData.value = { ...formData.value, ...parsed }
      } catch (error) {
        console.error('Error parsing saved form data:', error)
      }
    }
  }
})
</script>

<style scoped>
/* Ensure proper layout */
.min-h-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
</style>
