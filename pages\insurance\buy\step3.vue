<template>
  <div class="min-h-screen bg-white">
    <!-- Main Content -->
    <main class="max-w-[1920px] mx-auto px-4 lg:px-[390px] py-8">
      <!-- Steps Indicator -->
      <div class="mb-8">
        <StepsIndicator :current-step="3" />
      </div>

      <!-- Result Container -->
      <div class="bg-white border border-[#E9ECEE] rounded-lg p-8 max-w-[1140px] mx-auto">
        <!-- Success Result -->
        <div v-if="orderStatus === 'success'" class="text-center space-y-6">
          <!-- Success Icon -->
          <div class="flex justify-center">
            <div class="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center">
              <svg class="w-10 h-10 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
              </svg>
            </div>
          </div>

          <!-- Success Title -->
          <div>
            <h1 class="text-2xl font-semibold text-[#1A1A1A] mb-2">
              Mua bảo hiểm thành công!
            </h1>
            <p class="text-[#666666] text-base">
              Đơn hàng của bạn đã được xử lý thành công. Giấy chứng nhận bảo hiểm sẽ được gửi đến email của bạn trong thời gian sớm nhất.
            </p>
          </div>

          <!-- Order Information -->
          <div class="bg-gray-50 p-6 rounded-lg text-left space-y-4">
            <h3 class="text-lg font-medium text-[#333333] mb-4">Thông tin đơn hàng</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <span class="text-sm text-[#666666]">Mã đơn hàng:</span>
                <p class="font-medium text-[#333333]">{{ orderInfo.orderId }}</p>
              </div>
              <div>
                <span class="text-sm text-[#666666]">Biển kiểm soát:</span>
                <p class="font-medium text-[#333333]">{{ orderInfo.licensePlate }}</p>
              </div>
              <div>
                <span class="text-sm text-[#666666]">Thời gian đặt hàng:</span>
                <p class="font-medium text-[#333333]">{{ orderInfo.orderTime }}</p>
              </div>
              <div>
                <span class="text-sm text-[#666666]">Tổng phí:</span>
                <p class="font-medium text-[#0D68B2]">293.333đ</p>
              </div>
            </div>
          </div>

          <!-- Action Button -->
          <div class="pt-4">
            <button
              @click="goToHomePage"
              class="px-8 py-3 bg-[#0D68B2] text-white rounded-md font-medium hover:bg-[#0B5A9A] transition-colors"
            >
              Quay lại trang chủ
            </button>
          </div>
        </div>

        <!-- Failed Result -->
        <div v-else class="text-center space-y-6">
          <!-- Error Icon -->
          <div class="flex justify-center">
            <div class="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center">
              <svg class="w-10 h-10 text-red-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </div>
          </div>

          <!-- Error Title -->
          <div>
            <h1 class="text-2xl font-semibold text-[#1A1A1A] mb-2">
              Mua bảo hiểm thất bại!
            </h1>
            <p class="text-[#666666] text-base">
              Đã có lỗi xảy ra trong quá trình xử lý đơn hàng của bạn. Vui lòng thử lại sau hoặc liên hệ với chúng tôi để được hỗ trợ.
            </p>
          </div>

          <!-- Error Details -->
          <div class="bg-red-50 p-6 rounded-lg text-left">
            <h3 class="text-lg font-medium text-[#333333] mb-2">Chi tiết lỗi</h3>
            <p class="text-[#666666]">
              Hệ thống đang bảo trì hoặc có lỗi kỹ thuật. Vui lòng thử lại sau ít phút.
            </p>
          </div>

          <!-- Action Buttons -->
          <div class="flex flex-col sm:flex-row gap-4 justify-center pt-4">
            <button
              @click="retryOrder"
              class="px-6 py-3 bg-[#0D68B2] text-white rounded-md font-medium hover:bg-[#0B5A9A] transition-colors"
            >
              Thử lại
            </button>
            <button
              @click="goToHomePage"
              class="px-6 py-3 border border-[#E9ECEE] text-[#333333] rounded-md font-medium hover:bg-gray-50 transition-colors"
            >
              Quay lại trang chủ
            </button>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<script setup lang="ts">
import StepsIndicator from '~/components/StepsIndicator.vue'

// Set page title
useHead({
  title: 'Mua bảo hiểm - Bước 3: Kết quả'
})

// Order status
const orderStatus = ref<'success' | 'failed'>('success')

// Order information
const orderInfo = ref({
  orderId: '',
  licensePlate: '',
  orderTime: ''
})

// Methods
const goToHomePage = () => {
  // Clear session storage
  if (import.meta.client) {
    sessionStorage.removeItem('insuranceFormData')
    sessionStorage.removeItem('insuranceOrderStatus')
  }

  navigateTo('/')
}

const retryOrder = () => {
  navigateTo('/insurance/buy/step2')
}

// Generate order ID
const generateOrderId = () => {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `BIC${timestamp.slice(-6)}${random}`
}

// Load order status and data on mount
onMounted(() => {
  if (import.meta.client) {
    // Get order status
    const status = sessionStorage.getItem('insuranceOrderStatus')
    if (status) {
      orderStatus.value = status as 'success' | 'failed'
    }

    // Get form data for order info
    const savedData = sessionStorage.getItem('insuranceFormData')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        orderInfo.value = {
          orderId: generateOrderId(),
          licensePlate: parsed.licensePlate || 'N/A',
          orderTime: new Date().toLocaleString('vi-VN')
        }
      } catch (error) {
        console.error('Error parsing saved form data:', error)
      }
    }

    // If no status or data, redirect to step 1
    if (!status || !savedData) {
      navigateTo('/insurance/buy/step1')
    }
  }
})
</script>

<style scoped>
/* Ensure proper layout */
.min-h-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}

/* Custom animations */
.fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
