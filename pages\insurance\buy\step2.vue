<template>
  <div class="min-h-screen bg-white">
    <!-- Header -->
    <Header />

    <!-- Main Content -->
    <main class="max-w-[1920px] mx-auto px-4 lg:px-[390px] py-8">
      <!-- Steps Indicator -->
      <div class="mb-8">
        <StepsIndicator :current-step="2" />
      </div>

      <!-- Insurance Form -->
      <InsuranceForm
        form-title="Xác nhận thông tin"
        :readonly="true"
        :show-owner-info="true"
        :show-vehicle-info="true"
        :show-insurance-period="true"
        :show-fee-info="true"
        :show-agreement="true"
        :show-back-button="true"
        submit-button-text="Thanh toán"
        :initial-data="formData"
        @submit="handleSubmit"
        @back="handleBack"
      />
    </main>

    <!-- Footer -->
    <Footer />
  </div>
</template>

<script setup lang="ts">
import Header from '~/components/Header.vue'
import Footer from '~/components/Footer.vue'
import StepsIndicator from '~/components/StepsIndicator.vue'
import InsuranceForm from '~/components/InsuranceForm.vue'

// Set page title
useHead({
  title: 'Mua bảo hiểm - Bước 2: Xác nhận thông tin'
})

// Form data
const formData = ref({
  companyName: '',
  taxCode: '',
  companyAddress: '',
  phoneNumber: '',
  email: '',
  saveOwnerInfo: false,
  licensePlate: '',
  numberOfSeats: '',
  chassisNumber: '',
  engineNumber: '',
  startDate: '',
  agreement: false
})

// Handle form submission
const handleSubmit = async (data: any) => {
  try {
    // Here you would typically call an API to create the insurance order
    // For now, we'll simulate the process
    
    // Save final data
    if (process.client) {
      sessionStorage.setItem('insuranceFormData', JSON.stringify(data))
      sessionStorage.setItem('insuranceOrderStatus', 'success') // or 'failed'
    }
    
    // Navigate to step 3
    navigateTo('/insurance/buy/step3')
  } catch (error) {
    console.error('Error submitting insurance order:', error)
    
    // Set failed status and navigate to step 3
    if (process.client) {
      sessionStorage.setItem('insuranceOrderStatus', 'failed')
    }
    navigateTo('/insurance/buy/step3')
  }
}

// Handle back button
const handleBack = () => {
  navigateTo('/insurance/buy/step1')
}

// Load saved data on mount
onMounted(() => {
  if (process.client) {
    const savedData = sessionStorage.getItem('insuranceFormData')
    if (savedData) {
      try {
        const parsed = JSON.parse(savedData)
        formData.value = { ...formData.value, ...parsed }
      } catch (error) {
        console.error('Error parsing saved form data:', error)
        // If no data, redirect to step 1
        navigateTo('/insurance/buy/step1')
      }
    } else {
      // If no data, redirect to step 1
      navigateTo('/insurance/buy/step1')
    }
  }
})
</script>

<style scoped>
/* Ensure proper layout */
.min-h-screen {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}

main {
  flex: 1;
}
</style>
