<template>
  <div class="flex flex-col items-center justify-center w-full">
    <!-- Title Section -->
    <div class="flex flex-col items-center gap-[9px] mb-10">
      <div class="flex flex-col items-center">
        <h1
          class="font-['Helvetica_Neue:Bold',_sans-serif] text-[40px] text-center text-[#525252] leading-[1.2] font-bold">
          <PERSON><PERSON>o hiể<PERSON> bắt buộc trách nhiệm dân sự của chủ xe ô tô
        </h1>
      </div>
      <div class="flex flex-col items-center w-full">
        <p class="font-['Helvetica_Neue:Regular',_sans-serif] text-[16px] text-center text-[#525252] leading-[1.2]">
          Vui lòng cung cấp đầy đủ các thông tin dưới đây để tiếp tục.
        </p>
      </div>
    </div>

    <!-- Steps Section -->
    <div class="relative flex items-center justify-center">
      <!-- Step 1 -->
      <div class="flex flex-col items-center gap-2 w-[143px]">
        <div :class="[
          'w-[60px] h-[60px] rounded-[30px] flex items-center justify-center transition-colors',
          currentStep >= 1
            ? 'bg-[#3079ff]'
            : 'border border-[#3079ff] bg-white'
        ]">
          <span :class="[
            'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[28px] font-bold leading-[1.2] transition-colors',
            currentStep >= 1 ? 'text-white' : 'text-[#3079ff]'
          ]">
            1
          </span>
        </div>
        <div class="flex flex-col items-center w-full">
          <p :class="[
            'text-[16px] text-center leading-[1.2] transition-colors',
            currentStep >= 1
              ? 'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[#3079ff] font-bold'
              : 'font-[\'Helvetica_Neue:Regular\',_sans-serif] text-[#000000]'
          ]">
            Khai báo thông tin mua bảo hiểm
          </p>
        </div>
      </div>

      <!-- Connector 1 -->
      <div class="w-[257px] h-0 mx-4 relative">
        <div class="absolute bottom-0 left-0 right-0 top-[-2px]">
          <svg xmlns="http://www.w3.org/2000/svg" width="257" height="2" viewBox="0 0 257 2" fill="none"
            class="block max-w-none size-full">
            <line x1="8.74228e-08" y1="1" x2="257" y2="1.00002" stroke="#3079FF" stroke-width="2"
              :stroke-dasharray="currentStep >= 2 ? '0' : '4 4'" />
          </svg>
        </div>
      </div>

      <!-- Step 2 -->
      <div class="flex flex-col items-center gap-2 w-[143px]">
        <div :class="[
          'w-[60px] h-[60px] rounded-[30px] flex items-center justify-center transition-colors',
          currentStep >= 2
            ? 'bg-[#3079ff]'
            : 'border border-[#3079ff] bg-white'
        ]">
          <span :class="[
            'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[28px] font-bold leading-[1.2] transition-colors',
            currentStep >= 2 ? 'text-white' : 'text-[#3079ff]'
          ]">
            2
          </span>
        </div>
        <div class="flex flex-col items-center w-full">
          <p :class="[
            'text-[16px] text-center leading-[1.2] transition-colors',
            currentStep >= 2
              ? 'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[#3079ff] font-bold'
              : 'font-[\'Helvetica_Neue:Regular\',_sans-serif] text-[#000000]'
          ]">
            Xác nhận thông tin
          </p>
        </div>
      </div>

      <!-- Connector 2 -->
      <div class="w-[253px] h-0 mx-4 relative flex items-center justify-center">
        <div class="flex-none rotate-[180deg]">
          <div class="h-0 relative w-[253px]">
            <div class="absolute bottom-0 left-0 right-0 top-[-2px]">
              <svg xmlns="http://www.w3.org/2000/svg" width="257" height="2" viewBox="0 0 257 2" fill="none"
            class="block max-w-none size-full">
            <line x1="8.74228e-08" y1="1" x2="257" y2="1.00002" stroke="#3079FF" stroke-width="2"
              :stroke-dasharray="currentStep >= 2 ? '0' : '4 4'" />
          </svg>
            </div>
          </div>
        </div>
      </div>

      <!-- Step 3 -->
      <div class="flex flex-col items-center gap-2 w-[143px]">
        <div :class="[
          'w-[60px] h-[60px] rounded-[30px] flex items-center justify-center transition-colors',
          currentStep >= 3
            ? 'bg-[#3079ff]'
            : 'border border-[#3079ff] bg-white'
        ]">
          <span :class="[
            'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[28px] font-bold leading-[1.2] transition-colors',
            currentStep >= 3 ? 'text-white' : 'text-[#3079ff]'
          ]">
            3
          </span>
        </div>
        <div class="flex flex-col items-center w-full">
          <p :class="[
            'text-[16px] text-center leading-[1.2] transition-colors',
            currentStep >= 3
              ? 'font-[\'Helvetica_Neue:Bold\',_sans-serif] text-[#3079ff] font-bold'
              : 'font-[\'Helvetica_Neue:Regular\',_sans-serif] text-[#000000]'
          ]">
            Thanh toán
          </p>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentStep: number
}

withDefaults(defineProps<Props>(), {
  currentStep: 1
})
</script>

<style scoped>
/* Import Helvetica Neue font */
@import url('https://fonts.googleapis.com/css2?family=Helvetica+Neue:wght@400;700&display=swap');

/* Ensure smooth transitions */
.transition-colors {
  transition: background-color 0.3s ease, color 0.3s ease;
}

/* Fallback for Helvetica Neue font */
.font-helvetica-neue-regular {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 400;
}

.font-helvetica-neue-bold {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif;
  font-weight: 700;
}

/* Override Tailwind font classes to use Helvetica Neue */
[class*="font-['Helvetica_Neue:Regular'"] {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif !important;
  font-weight: 400 !important;
}

[class*="font-['Helvetica_Neue:Bold'"] {
  font-family: 'Helvetica Neue', 'Helvetica', 'Arial', sans-serif !important;
  font-weight: 700 !important;
}
</style>
