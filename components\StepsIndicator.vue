<template>
  <div class="flex items-center justify-center w-full">
    <div class="flex items-center gap-4">
      <!-- Step 1 -->
      <div class="flex items-center">
        <div 
          :class="[
            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
            currentStep >= 1 
              ? 'bg-[#0D68B2] text-white' 
              : 'bg-[#E9ECEE] text-[#9CA3AF]'
          ]"
        >
          1
        </div>
        <span 
          :class="[
            'ml-2 text-sm font-medium transition-colors',
            currentStep >= 1 ? 'text-[#0D68B2]' : 'text-[#9CA3AF]'
          ]"
        >
          Khai báo thông tin
        </span>
      </div>

      <!-- Connector 1 -->
      <div 
        :class="[
          'w-12 h-0.5 transition-colors',
          currentStep >= 2 ? 'bg-[#0D68B2]' : 'bg-[#E9ECEE]'
        ]"
      ></div>

      <!-- Step 2 -->
      <div class="flex items-center">
        <div 
          :class="[
            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
            currentStep >= 2 
              ? 'bg-[#0D68B2] text-white' 
              : 'bg-[#E9ECEE] text-[#9CA3AF]'
          ]"
        >
          2
        </div>
        <span 
          :class="[
            'ml-2 text-sm font-medium transition-colors',
            currentStep >= 2 ? 'text-[#0D68B2]' : 'text-[#9CA3AF]'
          ]"
        >
          Xác nhận thông tin
        </span>
      </div>

      <!-- Connector 2 -->
      <div 
        :class="[
          'w-12 h-0.5 transition-colors',
          currentStep >= 3 ? 'bg-[#0D68B2]' : 'bg-[#E9ECEE]'
        ]"
      ></div>

      <!-- Step 3 -->
      <div class="flex items-center">
        <div 
          :class="[
            'w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium transition-colors',
            currentStep >= 3 
              ? 'bg-[#0D68B2] text-white' 
              : 'bg-[#E9ECEE] text-[#9CA3AF]'
          ]"
        >
          3
        </div>
        <span 
          :class="[
            'ml-2 text-sm font-medium transition-colors',
            currentStep >= 3 ? 'text-[#0D68B2]' : 'text-[#9CA3AF]'
          ]"
        >
          Kết quả
        </span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
interface Props {
  currentStep: number
}

const props = withDefaults(defineProps<Props>(), {
  currentStep: 1
})
</script>

<style scoped>
/* Ensure smooth transitions */
.transition-colors {
  transition: background-color 0.3s ease, color 0.3s ease;
}
</style>
